-- ALTER queries for existing RLS policies
-- Run these in Supabase SQL Editor to modify existing policies

-- =============================================
-- 1. DROP AND RECREATE STORAGE POLICIES
-- =============================================

-- Drop existing storage policies if they exist
DROP POLICY IF EXISTS "Authenticated users can upload images" ON storage.objects;
DROP POLICY IF EXISTS "Users can update own images" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete own images" ON storage.objects;
DROP POLICY IF EXISTS "Public read access for images" ON storage.objects;

-- Recreate storage policies with correct permissions
CREATE POLICY "Authenticated users can upload images" ON storage.objects
FOR INSERT TO authenticated
WITH CHECK (bucket_id = 'profileimg');

CREATE POLICY "Users can update own images" ON storage.objects
FOR UPDATE TO authenticated
USING (bucket_id = 'profileimg' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can delete own images" ON storage.objects
FOR DELETE TO authenticated
USING (bucket_id = 'profileimg' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Public read access for images" ON storage.objects
FOR SELECT TO public
USING (bucket_id = 'profileimg');

-- =============================================
-- 2. UPDATE BUCKET CONFIGURATION
-- =============================================

-- Ensure profileimg bucket is properly configured
UPDATE storage.buckets 
SET 
  public = true,
  file_size_limit = 5242880,
  allowed_mime_types = ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
WHERE id = 'profileimg';

-- =============================================
-- 3. DROP AND RECREATE RESUMES TABLE POLICIES
-- =============================================

-- Drop existing resume policies
DROP POLICY IF EXISTS "Users can insert own resumes" ON resumes;
DROP POLICY IF EXISTS "Users can view own resumes" ON resumes;
DROP POLICY IF EXISTS "Public can view public resumes" ON resumes;
DROP POLICY IF EXISTS "Users can update own resumes" ON resumes;
DROP POLICY IF EXISTS "Users can delete own resumes" ON resumes;

-- Recreate resume policies
CREATE POLICY "Users can insert own resumes" ON resumes
FOR INSERT TO authenticated
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view own resumes" ON resumes
FOR SELECT TO authenticated
USING (auth.uid() = user_id);

CREATE POLICY "Public can view public resumes" ON resumes
FOR SELECT TO public
USING (is_public = true);

CREATE POLICY "Users can update own resumes" ON resumes
FOR UPDATE TO authenticated
USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own resumes" ON resumes
FOR DELETE TO authenticated
USING (auth.uid() = user_id);

-- =============================================
-- 4. DROP AND RECREATE ANALYTICS POLICIES
-- =============================================

-- Drop existing analytics policies if they exist
DROP POLICY IF EXISTS "Public can insert analytics" ON analytics;
DROP POLICY IF EXISTS "Users can view own resume analytics" ON analytics;

-- Recreate analytics policies
CREATE POLICY "Public can insert analytics" ON analytics
FOR INSERT TO public
WITH CHECK (true);

CREATE POLICY "Users can view own resume analytics" ON analytics
FOR SELECT TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM resumes 
    WHERE resumes.id = analytics.resume_id 
    AND resumes.user_id = auth.uid()
  )
);

-- =============================================
-- 5. DROP AND RECREATE TEMPLATES POLICIES
-- =============================================

-- Drop existing template policies
DROP POLICY IF EXISTS "Public can view templates" ON templates;

-- Recreate template policies
CREATE POLICY "Public can view templates" ON templates
FOR SELECT TO public
USING (true);

-- =============================================
-- 6. DROP AND RECREATE THEMES POLICIES
-- =============================================

-- Drop existing theme policies
DROP POLICY IF EXISTS "Public can view themes" ON themes;

-- Recreate theme policies
CREATE POLICY "Public can view themes" ON themes
FOR SELECT TO public
USING (true);

-- =============================================
-- 7. DROP AND RECREATE USER SUBSCRIPTION POLICIES
-- =============================================

-- Drop existing subscription policies if they exist
DROP POLICY IF EXISTS "Users can view own subscription" ON user_subscriptions;
DROP POLICY IF EXISTS "Users can update own subscription" ON user_subscriptions;

-- Recreate subscription policies
CREATE POLICY "Users can view own subscription" ON user_subscriptions
FOR SELECT TO authenticated
USING (auth.uid() = user_id);

CREATE POLICY "Users can update own subscription" ON user_subscriptions
FOR UPDATE TO authenticated
USING (auth.uid() = user_id);

-- =============================================
-- 8. ALTERNATIVE: MODIFY EXISTING POLICIES
-- =============================================

-- If you prefer to modify existing policies instead of dropping/recreating:

-- Update storage policy for authenticated uploads
-- ALTER POLICY "existing_upload_policy_name" ON storage.objects 
-- USING (bucket_id = 'profileimg' AND auth.role() = 'authenticated');

-- Update resume select policy to include public access
-- ALTER POLICY "existing_resume_select_policy" ON resumes 
-- USING (auth.uid() = user_id OR is_public = true);

-- =============================================
-- 9. GRANT PERMISSIONS TO FUNCTIONS
-- =============================================

-- Ensure function permissions are correct
GRANT EXECUTE ON FUNCTION generate_unique_slug(TEXT, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION generate_unique_slug(TEXT, UUID) TO anon;

-- =============================================
-- 10. VERIFY POLICIES ARE APPLIED
-- =============================================

-- Check all policies are in place
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

-- Check storage policies
SELECT * FROM storage.policies ORDER BY name;

-- Check bucket configuration
SELECT id, name, public, file_size_limit, allowed_mime_types 
FROM storage.buckets 
WHERE id = 'profileimg';

-- =============================================
-- 11. TROUBLESHOOTING QUERIES
-- =============================================

-- If you're still having issues, run these to debug:

-- Check current user
-- SELECT auth.uid(), auth.role();

-- Test storage access
-- SELECT * FROM storage.objects WHERE bucket_id = 'profileimg' LIMIT 5;

-- Test resume access
-- SELECT id, title, user_id, is_public FROM resumes LIMIT 5;

-- Check if RLS is enabled
-- SELECT schemaname, tablename, rowsecurity 
-- FROM pg_tables 
-- WHERE schemaname = 'public' AND rowsecurity = true;
